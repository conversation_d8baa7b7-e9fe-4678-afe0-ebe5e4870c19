import React from 'react'
import UrlFrom from '../components/url_from'
import UserUrlList from '../components/UserUrls'
// import UserUrl from '../components/UserUrl'

const DashboardPage = () => {
  return (
    <div className="min-h-screen w-full relative">
      {/* Radial Gradient Background from Bottom */}
      <div
        className="absolute inset-0 z-0"
        style={{
          background: "radial-gradient(125% 125% at 50% 90%, #fff 40%, #6366f1 100%)",
        }}
      />

      {/* Your Content/Components */}
      <div className="relative z-10 flex flex-col items-center justify-center p-4 min-h-screen">
        <div className="bg-white -mt-20 p-8 rounded-lg shadow-md w-full max-w-4xl">
          <h1 className="text-2xl font-bold text-center mb-6">URL Shortener</h1>
          <UrlFrom/>
          <UserUrlList/>
        </div>
      </div>
    </div>
  )
}

export default DashboardPage
