import React, { useState } from 'react'
import { createShortUrl } from '../api/shortUrl.api'
import { useSelector } from 'react-redux'
// import { QueryClient } from '@tanstack/react-query'
import { queryClient } from '../main'

const UrlForm = () => {
  
  const [url, setUrl] = useState("")
  const [shortUrl, setShortUrl] = useState()
  const [copied, setCopied] = useState(false)
  const [error, setError] = useState(null)
  const [customSlug, setCustomSlug] = useState("")
   const [isShortened, setIsShortened] = useState(false)
  const {isAuthenticated} = useSelector((state) => state.auth)

  const handleSubmit = async () => {
    console.log("Submitting URL:", url, "Custom slug:", customSlug)

    if (!url.trim()) {
      setError("Please enter a URL")
      return
    }

    try{
      console.log("Calling createShortUrl API...")
      const shortUrlResponse = await createShortUrl(url, customSlug)
      console.log("Received short URL:", shortUrlResponse)
      setShortUrl(shortUrlResponse)
      setIsShortened(true)
      queryClient.invalidateQueries({queryKey: ['userUrls']})
      setError(null)
    }catch(err){
      console.error("Error creating short URL:", err)
      setError(err.response?.data?.message || err.message || "Failed to create short URL")
    }
  }

  const handleShorten = handleSubmit

  const handleCopy = () => {
    navigator.clipboard.writeText(shortUrl);
    setCopied(true);
    
    // Reset the copied state after 2 seconds
    setTimeout(() => {
      setCopied(false);
    }, 2000);
  }

  return (
     <div className="max-w-2xl mx-auto">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-indigo-200 p-6 shadow-xl">
                {!isShortened ? (
                  <div className="flex flex-col sm:flex-row gap-3">
                    <div className="flex-1">
                      <input
                        type="url"
                        placeholder="Shorten any link..."
                        value={url}
                        onChange={(e) => setUrl(e.target.value)}
                        className="w-full h-12 text-lg px-4 border border-gray-200 rounded-lg focus:border-indigo-300 focus:ring-2 focus:ring-indigo-200 focus:outline-none"
                      />
                    </div>
                    <button
                      onClick={handleShorten}
                      className="bg-indigo-500 hover:bg-indigo-600 text-white px-8 h-12 text-lg rounded-lg font-semibold transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                      disabled={!url}
                    >
                      Shorten link
                    </button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center">
                          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                          </svg>
                        </div>
                        <div>
                          <div className="font-semibold text-gray-900">{shortUrl}</div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">{url}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center text-sm text-gray-600">
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                          </svg>
                          Analytics
                        </div>
                        <button
                          onClick={handleCopy}
                          className="flex items-center space-x-1 px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                          <span>{copied ? "Copied!" : "Copy"}</span>
                        </button>
                      </div>
                    </div>
                    <button
                      onClick={() => {
                        setIsShortened(false)
                        setUrl("")
                        setShortUrl("")
                      }}
                      className="text-gray-600 hover:text-gray-900 underline transition-colors duration-200"
                    >
                      Shorten another link
                    </button>
                  </div>
                )}
                {error && (
                  <div className="mt-4 p-3 bg-red-50 text-red-700 rounded-lg border border-red-200">
                    {error}
                  </div>
                )}
              </div>
            </div>
  )
}

export default UrlForm