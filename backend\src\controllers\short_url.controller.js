
import { getShortUrl } from "../dao/short_url.js";
import { createShortUrlWithoutUser, createShortUrlWithUser } from "../services/short_url.service.js";
import wrapAsync from "../utils/tryCatchWrapper.js";

export const createShortUrl = wrapAsync(async (req, res ) => {
        const data = req.body;
        let shortUrl
        if(req.user){
        shortUrl = await createShortUrlWithUser(data.url,req.user._id, data.slug)
        }else{
         shortUrl = await createShortUrlWithoutUser(data.url)
        }
        // res.status(403).send("Not allowed")
        res.status(200).json({shortUrl: process.env.APP_URL  + shortUrl});
} )

export const redirectFromShortUrl = wrapAsync(async (req , res ) => {
        const {id} = req.params
        console.log("Redirecting short URL:", id)

        const url = await getShortUrl(id)
        console.log("Found URL:", url)

        if(!url) {
            console.log("Short URL not found:", id)
            return res.status(404).json({error: "Short URL not found"})
        }

        console.log("Redirecting to:", url.full_url)
        res.redirect(url.full_url)
    })

export const createCustomShortUrl = wrapAsync(async (req,res)=>{
    const {url, slug} = req.body
    const shortUrl = await createShortUrlWithoutUser(url, slug)
    res.status(200).json({shortUrl : process.env.APP_URL + shortUrl})
})