import Button from '@mui/material/Button';
import Input from '@mui/material/Input'
import { Dribbble, Facebook, Instagram, Link, Send, Twitter } from 'react-feather';


const Footer =() => {
    <footer className="bg-white text-gray-800 py-16 px-4 sm:px-6 lg:px-8 border-t border-gray-200">
          <div className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-4 gap-12">
            {/* Stay Connected */}
            <div>
              <h3 className="text-2xl font-bold mb-6">Stay Connected</h3>
              <p className="text-gray-600 mb-6">Join our newsletter for the latest updates and exclusive offers.</p>
              <div className="flex">
                <Input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 rounded-r-none border-gray-300 focus:border-rose-300 focus:ring-rose-200"
                />
                <Button className="rounded-l-none bg-gray-900 hover:bg-gray-800 text-white px-4">
                  <Send className="w-5 h-5" />
                </Button>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-2xl font-bold mb-6">Quick Links</h3>
              <ul className="space-y-3">
                <li>
                  <Link href="#" className="text-gray-600 hover:text-gray-900 transition-colors">
                    Home
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-gray-600 hover:text-gray-900 transition-colors">
                    About Us
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-gray-600 hover:text-gray-900 transition-colors">
                    Services
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-gray-600 hover:text-gray-900 transition-colors">
                    Products
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-gray-600 hover:text-gray-900 transition-colors">
                    Contact
                  </Link>
                </li>
              </ul>
            </div>

            {/* Contact Us */}
            <div>
              <h3 className="text-2xl font-bold mb-6">Contact Us</h3>
              <address className="not-italic space-y-3 text-gray-600">
                <p>789 Sample Avenue</p>
                <p>Placeholder City, ZZ 98765</p>
                <p>Phone: (*************</p>
                <p>Email: <EMAIL></p>
              </address>
            </div>

            {/* Follow Us */}
            <div>
              <h3 className="text-2xl font-bold mb-6">Follow Us</h3>
              <div className="flex space-x-4 mb-8">
                <Link
                  href="#"
                  className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors"
                >
                  <Facebook className="w-5 h-5 text-gray-600" />
                </Link>
                <Link
                  href="#"
                  className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors"
                >
                  <Twitter className="w-5 h-5 text-gray-600" />
                </Link>
                <Link
                  href="#"
                  className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors"
                >
                  <Instagram className="w-5 h-5 text-gray-600" />
                </Link>
                <Link
                  href="#"
                  className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors"
                >
                  <Dribbble className="w-5 h-5 text-gray-600" />
                </Link>
              </div>
              {/* Theme Toggle Placeholder
              <div className="flex items-center space-x-3">
                <Sun className="w-6 h-6 text-gray-600" />
                <div className="w-12 h-6 bg-gray-300 rounded-full flex items-center p-1">
                  <div className="w-4 h-4 bg-purple-600 rounded-full ml-auto"></div>
                </div>
                <Moon className="w-6 h-6 text-gray-600" />
              </div> */}
            </div>
          </div>

          <hr className="border-gray-200 my-12" />

          {/* Bottom Copyright and Legal Links */}
          <div className="max-w-7xl mx-auto flex flex-col md:flex-row justify-between items-center text-sm text-gray-600">
            <p>&copy; {new Date().getFullYear()} Your Company. All rights reserved.</p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link href="#" className="hover:text-gray-900 transition-colors">
                Privacy Policy
              </Link>
              <Link href="#" className="hover:text-gray-900 transition-colors">
                Terms of Service
              </Link>
              <Link href="#" className="hover:text-gray-900 transition-colors">
                Cookie Settings
              </Link>
            </div>
          </div>
        </footer>

}

export default Footer