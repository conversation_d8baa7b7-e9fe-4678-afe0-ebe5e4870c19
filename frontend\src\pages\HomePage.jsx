// import React from 'react'
// import UrlFrom from '../components/url_from'

// const HomePage = () => {
//   return (
//     <div className="min-h-screen w-full relative">
//       {/* Radial Gradient Background from Bottom */}
//       <div
//         className="absolute inset-0 z-0"
//         style={{
//           background: "radial-gradient(125% 125% at 50% 90%, #fff 40%, #6366f1 100%)",
//         }}
//       />

//       {/* Your Content/Components */}
//       <div className='relative z-10 flex items-center justify-center p-4 min-h-screen'>
//         <div className='bg-white rounded-lg shadow-xl p-8 w-full max-w-md'>
//           <div className='text-center mb-8'>
//             <h1 className='text-3xl font-bold text-gray-800 mb-2'>URL Shortener</h1>
//             <p className='text-gray-600'>Make your long URLs short and shareable</p>
//           </div>
//           <UrlFrom />
//         </div>
//       </div>
//     </div>
//   )
// }


// export default HomePage

// // {/* <div className='mt-6 p-4 bg-green-50 rounded-lg border border-green-200'>
// //   <h3 className='text-sm font-medium text-green-800 mb-2'>Your shortened URL:</h3>
// //   <div className='flex items-center space-x-2'>
// //     <input
// //       type='text'
   
// //       readOnly
// //       className='flex-1 px-3 py-2 bg-white border border-green-300 rounded text-sm'
// //     />
// //     <button
      
// //       className='px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded transition-colors'
// //     >
// //       Copy
// //     </button>
// //   </div>
// //   <button
    
// //     className='mt-3 text-sm text-green-700 hover:text-green-800 underline'
// //   >
// //     Shorten another URL
// //   </button>
// // </div> */}


import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Copy, BarChart3, Link2, Zap, Shield, Users } from "lucide-react"
import Link from "next/link"

export default function Homepage() {
  const [url, setUrl] = useState("")
  const [shortenedUrl, setShortenedUrl] = useState("")
  const [isShortened, setIsShortened] = useState(false)
  const [copied, setCopied] = useState(false)

  const handleShorten = () => {
    if (url) {
      setShortenedUrl("dub.co/abc123")
      setIsShortened(true)
    }
  }

  const handleCopy = () => {
    navigator.clipboard.writeText(`https://${shortenedUrl}`)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <div className="min-h-screen w-full bg-[#faf9f6] relative">
      {/* Paper Texture */}
      <div
        className="absolute inset-0 z-0"
        style={{
          backgroundImage: `
        radial-gradient(circle at 1px 1px, rgba(0,0,0,0.08) 1px, transparent 0),
        repeating-linear-gradient(0deg, transparent, transparent 2px, rgba(0,0,0,0.02) 2px, rgba(0,0,0,0.02) 4px),
        repeating-linear-gradient(90deg, transparent, transparent 2px, rgba(0,0,0,0.02) 2px, rgba(0,0,0,0.02) 4px)
      `,
          backgroundSize: "8px 8px, 32px 32px, 32px 32px",
        }}
      />

      {/* Content with relative positioning to appear above texture */}
      <div className="relative z-10">
        {/* Sticky Navbar */}
        <nav className="sticky top-0 z-50 bg-white/20 backdrop-blur-xl border-b border-white/20 shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <Link href="/" className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-gradient-to-r from-rose-500 to-orange-500 rounded-lg flex items-center justify-center shadow-lg">
                    <Link2 className="w-5 h-5 text-white" />
                  </div>
                  <span className="text-xl font-bold text-gray-900">LinkShort</span>
                </Link>
              </div>

              <div className="hidden md:flex items-center space-x-8">
                <Link href="/pricing" className="text-gray-700 hover:text-gray-900 transition-colors font-medium">
                  Pricing
                </Link>
                <Link href="/login" className="text-gray-700 hover:text-gray-900 transition-colors font-medium">
                  Login
                </Link>
                <Button className="bg-gray-900/90 hover:bg-gray-800 text-white px-6 shadow-lg backdrop-blur-sm">
                  Sign Up
                </Button>
              </div>

              {/* Mobile menu button */}
              <div className="md:hidden">
                <Button variant="ghost" size="sm" className="text-gray-700 hover:text-gray-900 hover:bg-white/30">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                </Button>
              </div>
            </div>
          </div>
        </nav>

        {/* Hero Section */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-24">
          <div className="text-center">
            {/* Announcement Banner */}
            <div className="inline-flex items-center px-4 py-2 bg-white/60 backdrop-blur-sm rounded-full border border-rose-200 mb-8">
              <Zap className="w-4 h-4 text-orange-500 mr-2" />
              <span className="text-sm text-gray-700">Introducing Link Folders</span>
              <Link href="#" className="text-sm text-orange-600 ml-2 hover:underline">
                Read more →
              </Link>
            </div>

            {/* Main Headline */}
            <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight">
              Short links with
              <br />
              <span className="bg-gradient-to-r from-rose-600 to-orange-600 bg-clip-text text-transparent">
                superpowers
              </span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl md:text-2xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
              Open-source link management platform for marketing teams
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
              <Button size="lg" className="bg-gray-900 hover:bg-gray-800 text-white px-8 py-4 text-lg">
                Start for free
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-gray-300 text-gray-700 hover:bg-gray-50 px-8 py-4 text-lg bg-transparent"
              >
                Get a demo
              </Button>
            </div>

            {/* URL Shortener Interface */}
            <div className="max-w-2xl mx-auto">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-rose-200 p-6 shadow-xl">
                {!isShortened ? (
                  <div className="flex flex-col sm:flex-row gap-3">
                    <div className="flex-1">
                      <Input
                        type="url"
                        placeholder="Shorten any link..."
                        value={url}
                        onChange={(e) => setUrl(e.target.value)}
                        className="h-12 text-lg border-gray-200 focus:border-rose-300 focus:ring-rose-200"
                      />
                    </div>
                    <Button
                      onClick={handleShorten}
                      className="bg-orange-500 hover:bg-orange-600 text-white px-8 h-12 text-lg"
                      disabled={!url}
                    >
                      Shorten link
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-rose-500 to-orange-500 rounded-full flex items-center justify-center">
                          <Link2 className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <div className="font-semibold text-gray-900">{shortenedUrl}</div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">{url}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center text-sm text-gray-600">
                          <BarChart3 className="w-4 h-4 mr-1" />
                          69.2K clicks
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleCopy}
                          className="flex items-center space-x-1 bg-transparent"
                        >
                          <Copy className="w-4 h-4" />
                          <span>{copied ? "Copied!" : "Copy"}</span>
                        </Button>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      onClick={() => {
                        setIsShortened(false)
                        setUrl("")
                        setShortenedUrl("")
                      }}
                      className="text-gray-600 hover:text-gray-900"
                    >
                      Shorten another link
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {/* Trusted by section */}
            <div className="mt-20">
              <p className="text-sm text-gray-500 mb-8">Trusted by teams at</p>
              <div className="grid grid-cols-2 md:grid-cols-6 gap-8 items-center opacity-60">
                {["Vercel", "Twilio", "Raycast", "Framer", "Buffer", "Superhuman"].map((company) => (
                  <div key={company} className="text-gray-400 font-semibold text-lg">
                    {company}
                  </div>
                ))}
              </div>
            </div>

            {/* Features Section */}
            <div className="mt-32">
              <div className="text-center mb-16">
                <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                  Powerful features for
                  <br />
                  modern marketing teams
                </h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  More than just a link shortener, we built a suite of powerful features that give your marketing
                  superpowers.
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-8">
                <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-rose-200">
                  <div className="w-12 h-12 bg-gradient-to-r from-rose-500 to-orange-500 rounded-lg flex items-center justify-center mb-6">
                    <BarChart3 className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Advanced Analytics</h3>
                  <p className="text-gray-600">
                    Get detailed insights into your link performance with real-time analytics and comprehensive
                    reporting.
                  </p>
                </div>

                <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-rose-200">
                  <div className="w-12 h-12 bg-gradient-to-r from-rose-500 to-orange-500 rounded-lg flex items-center justify-center mb-6">
                    <Shield className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Custom Domains</h3>
                  <p className="text-gray-600">
                    Use your own branded domain to build trust and maintain brand consistency across all your links.
                  </p>
                </div>

                <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-rose-200">
                  <div className="w-12 h-12 bg-gradient-to-r from-rose-500 to-orange-500 rounded-lg flex items-center justify-center mb-6">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Team Collaboration</h3>
                  <p className="text-gray-600">
                    Work together with your team to manage links, share insights, and collaborate on campaigns.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
