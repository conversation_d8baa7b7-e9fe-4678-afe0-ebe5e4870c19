import React from 'react'
import UrlFrom from '../components/url_from'

const HomePage = () => {
  return (
    <div className="min-h-screen w-full relative">
      {/* Radial Gradient Background from Bottom */}
      <div
        className="absolute inset-0 z-0"
        style={{
          background: "radial-gradient(125% 125% at 50% 90%, #fff 40%, #6366f1 100%)",
        }}
      />

      {/* Your Content/Components */}
      <div className='relative z-10 flex items-center justify-center p-4 min-h-screen'>
        <div className='bg-white rounded-lg shadow-xl p-8 w-full max-w-md'>
          <div className='text-center mb-8'>
            <h1 className='text-3xl font-bold text-gray-800 mb-2'>URL Shortener</h1>
            <p className='text-gray-600'>Make your long URLs short and shareable</p>
          </div>
          <UrlFrom />
        </div>
      </div>
    </div>
  )
}

export default HomePage

{/* <div className='mt-6 p-4 bg-green-50 rounded-lg border border-green-200'>
  <h3 className='text-sm font-medium text-green-800 mb-2'>Your shortened URL:</h3>
  <div className='flex items-center space-x-2'>
    <input
      type='text'
   
      readOnly
      className='flex-1 px-3 py-2 bg-white border border-green-300 rounded text-sm'
    />
    <button
      
      className='px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded transition-colors'
    >
      Copy
    </button>
  </div>
  <button
    
    className='mt-3 text-sm text-green-700 hover:text-green-800 underline'
  >
    Shorten another URL
  </button>
</div> */}