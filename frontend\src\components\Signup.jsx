"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Chrome, Github } from "lucide-react"

export default function SignupForm({ onSwitchToLogin }) {
  return (
    <div className="bg-white p-8 rounded-lg shadow-lg w-full border border-gray-200">
      <h2 className="text-2xl font-bold text-center mb-8 text-gray-900">Create your LinkShort account</h2>

      <div className="space-y-4">
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1 text-left">
            Email
          </label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-rose-500 focus:border-rose-500"
          />
        </div>
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1 text-left">
            Password
          </label>
          <Input
            id="password"
            type="password"
            placeholder="••••••••"
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-rose-500 focus:border-rose-500"
          />
        </div>
        <Button className="w-full bg-gray-900 hover:bg-gray-800 text-white py-2">Sign Up</Button>
      </div>

      <div className="relative my-8">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-gray-300" />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="bg-white px-2 text-gray-500">OR</span>
        </div>
      </div>

      <div className="space-y-3">
        <Button
          variant="outline"
          className="w-full flex items-center justify-center space-x-2 py-2 border-gray-300 text-gray-700 hover:bg-gray-50 bg-transparent"
        >
          <Chrome className="w-5 h-5" />
          <span>Continue with Google</span>
        </Button>
        <Button
          variant="outline"
          className="w-full flex items-center justify-center space-x-2 py-2 border-gray-300 text-gray-700 hover:bg-gray-50 bg-transparent"
        >
          <Github className="w-5 h-5" />
          <span>Continue with Github</span>
        </Button>
      </div>

      <p className="text-center text-sm text-gray-600 mt-8">
        Already have an account?{" "}
        <button onClick={onSwitchToLogin} className="text-rose-600 hover:underline font-medium">
          Log in
        </button>
      </p>
    </div>
  )
}
