{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroui/button": "^2.2.22", "@heroui/system": "^2.4.18", "@heroui/theme": "^2.4.17", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.10", "@tanstack/react-query": "^5.81.2", "@tanstack/react-router": "^1.121.34", "axios": "^1.10.0", "framer-motion": "^12.23.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-feather": "^2.0.10", "react-redux": "^9.2.0", "redux": "^5.0.1", "tailwindcss": "^4.1.10"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "vite": "^7.0.0"}}