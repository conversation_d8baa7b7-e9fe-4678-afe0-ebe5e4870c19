import { useState } from 'react'
import LoginForm from '../components/LoginFrom'
import RegisterForm from '../components/RegisterFrom'

const AuthPage = () => {

    const [login, setLogin] = useState(true)

  return (
    <div className="min-h-screen w-full relative">
      {/* Radial Gradient Background from Bottom */}
      <div
        className="absolute inset-0 z-0"
        style={{
          background: "radial-gradient(125% 125% at 50% 90%, #fff 40%, #6366f1 100%)",
        }}
      />

      {/* Your Content/Components */}
      <div className='relative z-10 flex items-center justify-center p-4 min-h-screen'>
        <div className='bg-white rounded-lg shadow-xl p-8 w-full max-w-md'>
          <div className='text-center mb-8'>
          </div>
          {login ?
          <LoginForm state={setLogin} /> : <RegisterForm state={setLogin}/> }
        </div>
      </div>
    </div>
  )
}

export default AuthPage
