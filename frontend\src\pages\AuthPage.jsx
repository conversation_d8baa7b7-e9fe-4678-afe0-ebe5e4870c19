import { useState } from 'react'
import LoginForm from '../components/LoginFrom'
import RegisterForm from '../components/RegisterFrom'
import { Link } from '@tanstack/react-router'

const AuthPage = () => {

    const [login, setLogin] = useState(true)

  return (
    <div className="min-h-screen w-full bg-[#f9fafb] relative flex items-center justify-center p-4">
      {/* Diagonal Fade Grid Background - Top Left */}
      <div
        className="absolute inset-0 z-0"
        style={{
          backgroundImage: `
            linear-gradient(to right, #d1d5db 1px, transparent 1px),
            linear-gradient(to bottom, #d1d5db 1px, transparent 1px)
          `,
          backgroundSize: "32px 32px",
          WebkitMaskImage: "radial-gradient(ellipse 80% 80% at 0% 0%, #000 50%, transparent 90%)",
          maskImage: "radial-gradient(ellipse 80% 80% at 0% 0%, #000 50%, transparent 90%)",
        }}
      />

      {/* Content */}
      <div className="relative z-10 flex flex-col items-center justify-center w-full max-w-md mx-auto">
        {/* Logo */}
        <div className="mb-8">
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-10 h-10 bg-gradient-to-r from-rose-500 to-orange-500 rounded-lg flex items-center justify-center shadow-lg">
              <Lock className="w-6 h-6 text-white" />
            </div>
            <span className="text-2xl font-bold text-gray-900">LinkShort</span>
          </Link>
        </div>


        {/* Partner Account Section */}
        <div className="bg-gray-100 p-4 rounded-lg border border-gray-200 mt-8 w-full text-center text-sm text-gray-600">
          <p>Looking for your LinkShort partner account?</p>
          <Link href="#" className="text-gray-800 hover:underline font-medium">
            Log in at partners.linkshort.co
          </Link>
        </div>

        {/* Legal Text */}
        <p className="text-center text-xs text-gray-500 mt-8 max-w-xs">
          By continuing, you agree to LinkShort's Terms of Service and Privacy Policy
        </p>
      </div>
    </div>
  )
}

export default AuthPage
