import { generateNanoId } from "../utils/helper.js"
import urlSchema from "../models/shorturl.model.js"
import { getCustomShortUrl, saveShortUrl } from "../dao/short_url.js"

export const createShortUrlWithoutUser = async (url) => {
    // Validate and normalize URL
    let normalizedUrl = url.trim()
    if (!normalizedUrl.startsWith('http://') && !normalizedUrl.startsWith('https://')) {
        normalizedUrl = 'https://' + normalizedUrl
    }

    // Validate URL format
    try {
        new URL(normalizedUrl)
    } catch (error) {
        throw new Error("Invalid URL format")
    }

    const shortUrl = generateNanoId(7)
    if(!shortUrl) throw new Error("Short URL not generated")

    console.log("Saving URL:", normalizedUrl, "with short URL:", shortUrl)
    await saveShortUrl(shortUrl, normalizedUrl)
    return shortUrl
}

export const createShortUrlWithUser = async (url,userId,slug=null) => {
    const shortUrl = slug || generateNanoId(7)
    const exists = await getCustomShortUrl(slug)
    if(exists) throw new Error("This custom url already exists")

    await saveShortUrl(shortUrl,url,userId)
    return shortUrl
}