import React from 'react'
import { Link } from '@tanstack/react-router'

const Navbar = () => {
  return (
    <nav className="bg-transparent border-b border-white/20 backdrop-blur-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Left side - Logo/Name */}
          <div className="flex items-center">
            <Link to="/" className="flex-shrink-0 flex items-center">
              <span className="text-xl font-semibold text-gray-800">URL Shortener</span>
            </Link>
          </div>

          {/* Right side - Login button */}
          <div className="flex items-center">
            <Link
              to="/auth"
              className="ml-4 px-4 py-2 rounded-md text-sm font-semibold text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
            >
              Login
            </Link>
          </div>
        </div>
      </div>
    </nav>
  )
}

export default Navbar