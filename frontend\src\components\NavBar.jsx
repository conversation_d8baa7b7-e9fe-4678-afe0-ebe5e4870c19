import React from 'react'
import { Link } from '@tanstack/react-router'

const Navbar = () => {
  return (
    <nav className="sticky top-0 z-50 bg-white/20 backdrop-blur-xl border-b border-white/20 shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <Link href="/" className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-gradient-to-r from-rose-500 to-orange-500 rounded-lg flex items-center justify-center shadow-lg">
                    <Link2 className="w-5 h-5 text-white" />
                  </div>
                  <span className="text-xl font-bold text-gray-900">LinkShort</span>
                </Link>
              </div>

              <div className="hidden md:flex items-center space-x-8">
                <Link href="/pricing" className="text-gray-700 hover:text-gray-900 transition-colors font-medium">
                  Pricing
                </Link>
                <Link href="/login" className="text-gray-700 hover:text-gray-900 transition-colors font-medium">
                  Login
                </Link>
                <Button className="bg-gray-900/90 hover:bg-gray-800 text-white px-6 shadow-lg backdrop-blur-sm">
                  Sign Up
                </Button>
              </div>

              {/* Mobile menu button */}
              <div className="md:hidden">
                <Button variant="ghost" size="sm" className="text-gray-700 hover:text-gray-900 hover:bg-white/30">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                </Button>
              </div>
            </div>
          </div>
        </nav>
  )
}

export default Navbar