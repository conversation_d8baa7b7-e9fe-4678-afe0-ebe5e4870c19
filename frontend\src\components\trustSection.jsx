import React from "react";
import { motion } from "framer-motion";
import { Zap, Users } from "react-feather";
import { motion } from "framer-motion";


const companiesRow1 = [
  {
    name: "Whop",
    icon: <div className="w-6 h-6 bg-gradient-to-r from-orange-400 to-red-500 rounded transform rotate-45"></div>,
    textClass: "text-xl font-bold text-gray-900",
  },
  {
    name: "clerk",
    icon: (
      <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-blue-500 rounded-full flex items-center justify-center">
        <Users className="w-4 h-4 text-white" />
      </div>
    ),
    textClass: "text-xl font-medium text-gray-700",
  },
  { name: "Cal.com", icon: null, textClass: "text-xl font-bold text-gray-900" },
  {
    name: "Weights & Biases",
    icon: (
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
        <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
        <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
      </div>
    ),
    textClass: "text-lg font-medium text-gray-800",
  },
  {
    name: "supabase",
    icon: <Zap className="w-6 h-6 text-green-500" />,
    textClass: "text-xl font-bold text-gray-900",
  },
]

const companiesRow2 = [
  {
    name: "Prisma",
    icon: <div className="w-6 h-6 bg-gray-800 rounded-sm transform rotate-45"></div>,
    textClass: "text-xl font-bold text-gray-900",
    subText: "CASE STUDY",
  },
  {
    name: "Polymarket",
    icon: (
      <div className="w-6 h-6 border-2 border-gray-700 rounded flex items-center justify-center">
        <div className="w-3 h-2 bg-gray-700 rounded-sm"></div>
      </div>
    ),
    textClass: "text-xl font-medium text-gray-800",
  },
  {
    name: "Sketch",
    icon: <div className="w-6 h-6 bg-gray-900 transform rotate-45 rounded-sm"></div>,
    textClass: "text-xl font-bold text-gray-900",
  },
  { name: "L'OCCITANE", icon: null, textClass: "text-lg font-serif text-gray-800", subText: "EN PROVENCE" },
  { name: "viator", icon: null, textClass: "text-xl font-bold text-green-600" },
]
const TrustSection = () => {
    

<div className="mt-20">
              <p className="text-sm text-gray-500 mb-12">Trusted by teams at</p>

              <div className="relative overflow-hidden py-4">
                <motion.div
                  className="flex flex-nowrap"
                  animate={{ x: ["0%", "-100%"] }}
                  transition={{
                    x: {
                      repeat: Number.POSITIVE_INFINITY,
                      repeatType: "loop",
                      duration: 30, // Adjust duration for speed
                      ease: "linear",
                    },
                  }}
                >
                  {/* Duplicate the content to create a seamless loop */}
                  {[...companiesRow1, ...companiesRow1].map((company, index) => (
                    <div key={index} className="flex-shrink-0 w-[200px] flex items-center justify-center mx-6">
                      <div className="flex items-center space-x-2">
                        {company.icon}
                        <span className={company.textClass}>{company.name}</span>
                      </div>
                    </div>
                  ))}
                </motion.div>
              </div>

              <div className="relative overflow-hidden py-4 mt-8">
                <motion.div
                  className="flex flex-nowrap"
                  animate={{ x: ["-100%", "0%"] }} // Reverse direction for second row
                  transition={{
                    x: {
                      repeat: Number.POSITIVE_INFINITY,
                      repeatType: "loop",
                      duration: 30, // Adjust duration for speed
                      ease: "linear",
                    },
                  }}
                >
                  {/* Duplicate the content to create a seamless loop */}
                  {[...companiesRow2, ...companiesRow2].map((company, index) => (
                    <div key={index} className="flex-shrink-0 w-[200px] flex flex-col items-center justify-center mx-6">
                      <div className="flex items-center space-x-2 mb-1">
                        {company.icon}
                        <span className={company.textClass}>{company.name}</span>
                      </div>
                      {company.subText && (
                        <span className="text-xs text-gray-500 font-medium tracking-wider">{company.subText}</span>
                      )}
                    </div>
                  ))}
                </motion.div>
              </div>
            </div>

                }
export default TrustSection;