import React from "react";


const companiesRow1 = [
  {
    name: "Who<PERSON>",
    icon: <div className="w-6 h-6 bg-gradient-to-r from-orange-400 to-red-500 rounded transform rotate-45"></div>,
    textClass: "text-xl font-bold text-gray-900",
  },
  {
    name: "clerk",
    icon: (
      <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-blue-500 rounded-full flex items-center justify-center">
        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      </div>
    ),
    textClass: "text-xl font-medium text-gray-700",
  },
  { name: "Cal.com", icon: null, textClass: "text-xl font-bold text-gray-900" },
  {
    name: "Weights & Biases",
    icon: (
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
        <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
        <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
      </div>
    ),
    textClass: "text-lg font-medium text-gray-800",
  },
  {
    name: "supabase",
    icon: (
      <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
    ),
    textClass: "text-xl font-bold text-gray-900",
  },
]

const companiesRow2 = [
  {
    name: "Prisma",
    icon: <div className="w-6 h-6 bg-gray-800 rounded-sm transform rotate-45"></div>,
    textClass: "text-xl font-bold text-gray-900",
    subText: "CASE STUDY",
  },
  {
    name: "Polymarket",
    icon: (
      <div className="w-6 h-6 border-2 border-gray-700 rounded flex items-center justify-center">
        <div className="w-3 h-2 bg-gray-700 rounded-sm"></div>
      </div>
    ),
    textClass: "text-xl font-medium text-gray-800",
  },
  {
    name: "Sketch",
    icon: <div className="w-6 h-6 bg-gray-900 transform rotate-45 rounded-sm"></div>,
    textClass: "text-xl font-bold text-gray-900",
  },
  { name: "L'OCCITANE", icon: null, textClass: "text-lg font-serif text-gray-800", subText: "EN PROVENCE" },
  { name: "viator", icon: null, textClass: "text-xl font-bold text-green-600" },
]
const TrustSection = () => {
  return (
    <div className="mt-20">
      <p className="text-sm text-gray-500 mb-12 text-center">Trusted by teams at</p>

      <div className="relative overflow-hidden py-4">
        <div className="flex flex-nowrap animate-scroll">
          {/* Duplicate the content to create a seamless loop */}
          {[...companiesRow1, ...companiesRow1].map((company, index) => (
            <div key={index} className="flex-shrink-0 w-[200px] flex items-center justify-center mx-6">
              <div className="flex items-center space-x-2">
                {company.icon}
                <span className={company.textClass}>{company.name}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="relative overflow-hidden py-4 mt-8">
        <div className="flex flex-nowrap animate-scroll-reverse">
          {/* Duplicate the content to create a seamless loop */}
          {[...companiesRow2, ...companiesRow2].map((company, index) => (
            <div key={index} className="flex-shrink-0 w-[200px] flex flex-col items-center justify-center mx-6">
              <div className="flex items-center space-x-2 mb-1">
                {company.icon}
                <span className={company.textClass}>{company.name}</span>
              </div>
              {company.subText && (
                <span className="text-xs text-gray-500 font-medium tracking-wider">{company.subText}</span>
              )}
            </div>
          ))}
        </div>
      </div>

      <style jsx>{`
        @keyframes scroll {
          0% { transform: translateX(0%); }
          100% { transform: translateX(-100%); }
        }

        @keyframes scroll-reverse {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(0%); }
        }

        .animate-scroll {
          animation: scroll 30s linear infinite;
        }

        .animate-scroll-reverse {
          animation: scroll-reverse 30s linear infinite;
        }
      `}</style>
    </div>
  );
};
export default TrustSection;